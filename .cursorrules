# Cursor Rules for Stiletto Proto - Godot Project

## Core Principles

### FUNCTIONAL PROGRAMMING PHILOSOPHY (MANDATORY)
**Turn EVERYTHING into a FUNCTION!** This is inspired by functional programming principles:

- **Pure Functions**: Each function does ONE THING only with clear inputs/outputs
- **No Side Effects**: Functions should be predictable and testable
- **Small, Self-Contained**: Break complex logic into tiny, focused functions
- **LLM-Friendly**: Each function is independent - breaking one doesn't break others
- **Clear Context**: LLMs can focus on ONE piece of logic at a time

### SOLID Principles (MANDATORY)
- **Single Responsibility**: Each class/script should have one clear purpose
- **Open/Closed**: Open for extension, closed for modification
- **Liskov Substitution**: Derived classes must be substitutable for their base classes
- **Interface Segregation**: Create focused interfaces rather than monolithic ones
- **Dependency Inversion**: Depend on abstractions, not concrete implementations

### DRY (Don't Repeat Yourself)
- Extract common functionality into reusable components
- Use composition over inheritance
- Create utility functions for repeated patterns
- Leverage Godot's resource system for shared data

### Modular & Composition-Based Architecture
- Build systems as composable components
- Use dependency injection patterns
- Create interfaces/abstract classes for flexibility
- Design for easy replacement and extension

## Godot-Specific Architecture Rules

### 1. Scene Structure & Organization
```
- Use clear, descriptive scene names
- Organize scenes hierarchically by feature
- Keep scenes focused and single-purpose
- Use autoloads sparingly and only for truly global systems
- Prefer composition over deep inheritance trees
```

### 2. Script Organization
```
- One script per scene (unless composition requires multiple)
- Use descriptive class names that indicate purpose
- Group related functionality into separate scripts
- Create base classes for common patterns
- Use interfaces for flexible component communication
```

### 3. Resource Management
```
- Create custom resources for complex data structures
- Use ResourceLoader for dynamic loading
- Implement proper resource cleanup
- Leverage Godot's resource system for configuration
- Create resource-based component systems
```

### 4. Component-Based Design
```
- Break complex systems into focused components
- Use composition to build complex behaviors
- Create interfaces for component communication
- Design components to be easily swapped/replaced
- Use signals for loose coupling between components
```

## Code Quality Standards

### 1. Naming Conventions
```
- Classes: PascalCase (e.g., PlayerController, EnemyAI)
- Functions: snake_case (e.g., move_player, calculate_damage)
- Variables: snake_case (e.g., player_health, enemy_speed)
- Constants: UPPER_SNAKE_CASE (e.g., MAX_HEALTH, DEFAULT_SPEED)
- Signals: snake_case with descriptive names (e.g., player_died, enemy_spotted)
```

### 2. Function Design (FUNCTIONAL APPROACH)
```
- Keep functions TINY and focused (max 10-15 lines)
- Each function does EXACTLY ONE THING
- Use descriptive parameter names with clear inputs/outputs
- Return early to reduce nesting
- Use type hints for clarity
- Document complex functions with comments
- PREFER PURE FUNCTIONS with no side effects
- Break complex logic into multiple small functions
- Each function should be independently testable
```

### 3. Error Handling
```
- Use proper error handling patterns
- Validate inputs at function boundaries
- Use Godot's built-in error handling
- Provide meaningful error messages
- Handle edge cases gracefully
```

## System-Specific Guidelines

### 1. AI Systems (NextBot, Knight AI) - FUNCTIONAL APPROACH
```
- Break AI logic into TINY, pure functions
- Each AI decision should be a separate function
- Use pure functions for state transitions
- Create small, focused behavior functions
- Implement functional interfaces for different AI types
- Use function composition for behavior mixing
- Design for easy behavior modification through function replacement
- Each AI function should be independently testable
- Avoid global state - pass data as parameters
```

### 2. Player Systems
```
- Separate input handling from game logic
- Use component-based player architecture
- Create modular weapon/ability systems
- Implement proper state management
- Design for easy feature addition/removal
```

### 3. Multiplayer Systems
```
- Separate network logic from game logic
- Use proper synchronization patterns
- Implement client-side prediction
- Create robust error handling for network issues
- Design for scalability and performance
```

### 4. UI Systems
```
- Create reusable UI components
- Separate UI logic from game logic
- Use proper signal communication
- Implement responsive design patterns
- Create modular UI systems
```

## Performance & Optimization

### 1. Memory Management
```
- Use object pooling for frequently created/destroyed objects
- Implement proper cleanup in _exit_tree()
- Avoid memory leaks in long-running systems
- Use weak references when appropriate
- Monitor memory usage in performance-critical systems
```

### 2. Performance Patterns
```
- Use batch processing for similar operations
- Implement spatial partitioning for large worlds
- Use LOD systems for complex geometry
- Optimize update loops for performance
- Profile and optimize bottlenecks
```

## Testing & Debugging

### 1. Code Organization for Testing
```
- Design systems to be easily testable
- Use dependency injection for testability
- Create mock objects for testing
- Separate business logic from engine dependencies
- Write self-documenting code
```

### 2. Debugging Support
```
- Add comprehensive logging
- Create debug visualization tools
- Implement proper error reporting
- Use Godot's built-in debugging features
- Create debug modes for development
```

## Documentation Standards

### 1. Code Documentation
```
- Document public interfaces
- Explain complex algorithms
- Use clear, concise comments
- Document assumptions and constraints
- Keep documentation up to date
```

### 2. Architecture Documentation
```
- Document system interactions
- Explain design decisions
- Create component diagrams
- Document data flow patterns
- Maintain system overviews
```

## Specific Implementation Patterns

### 1. Functional Component Pattern
```gdscript
# Example: Functional component system with pure functions
class_name HealthComponent
extends Node

signal health_changed(new_health, max_health)
signal died

@export var max_health: float = 100.0
var current_health: float

# Pure function - calculates new health without side effects
func calculate_new_health(current: float, damage: float, max_hp: float) -> float:
    return max(0, current - damage)

# Pure function - determines if entity is dead
func is_dead(health: float) -> bool:
    return health <= 0

# Pure function - calculates health percentage
func calculate_health_percentage(current: float, max_hp: float) -> float:
    return current / max_hp if max_hp > 0 else 0.0

# Main function that orchestrates the pure functions
func take_damage(amount: float) -> void:
    var new_health = calculate_new_health(current_health, amount, max_health)
    current_health = new_health
    health_changed.emit(current_health, max_health)
    
    if is_dead(current_health):
        died.emit()
```

### 2. Functional State Machine Pattern
```gdscript
# Example: Functional state machine with pure functions
class_name StateMachine
extends Node

var current_state: State
var states: Dictionary = {}

# Pure function - determines if state can transition
func can_transition_to(from_state: State, to_state: State) -> bool:
    # Define valid transitions
    var valid_transitions = {
        State.IDLE: [State.AWARE, State.CHASE],
        State.AWARE: [State.IDLE, State.CHASE],
        State.CHASE: [State.IDLE, State.MELEE, State.ATTACKING],
        State.MELEE: [State.CHASE, State.ATTACKING],
        State.ATTACKING: [State.CHASE, State.MELEE]
    }
    return to_state in valid_transitions.get(from_state, [])

# Pure function - validates state transition
func validate_transition(from_state: State, to_state: State) -> bool:
    return from_state != to_state and can_transition_to(from_state, to_state)

# Pure function - gets valid next states
func get_valid_next_states(current: State) -> Array[State]:
    var valid_transitions = {
        State.IDLE: [State.AWARE, State.CHASE],
        State.AWARE: [State.IDLE, State.CHASE],
        State.CHASE: [State.IDLE, State.MELEE, State.ATTACKING],
        State.MELEE: [State.CHASE, State.ATTACKING],
        State.ATTACKING: [State.CHASE, State.MELEE]
    }
    return valid_transitions.get(current, [])

# Main function that orchestrates the pure functions
func change_state(new_state: State) -> void:
    if validate_transition(current_state, new_state):
        if current_state:
            current_state.exit()
        current_state = new_state
        if current_state:
            current_state.enter()
```

### 3. Functional Event System Pattern
```gdscript
# Example: Functional event system with pure functions
class_name EventBus
extends Node

signal player_died
signal enemy_spotted(enemy)
signal level_completed

# Pure function - validates event data
func validate_event_data(event_type: String, data: Dictionary) -> bool:
    var required_fields = {
        "player_died": ["player_id", "cause"],
        "enemy_spotted": ["enemy_id", "position", "distance"],
        "level_completed": ["level_id", "score", "time"]
    }
    var fields = required_fields.get(event_type, [])
    return fields.all(func(field): return field in data)

# Pure function - formats event message
func format_event_message(event_type: String, data: Dictionary) -> String:
    return "[%s] %s" % [event_type, str(data)]

# Pure function - determines event priority
func get_event_priority(event_type: String) -> int:
    var priorities = {
        "player_died": 1,
        "enemy_spotted": 2,
        "level_completed": 3
    }
    return priorities.get(event_type, 0)

# Main function that orchestrates the pure functions
func emit_event(event_type: String, data: Dictionary) -> void:
    if validate_event_data(event_type, data):
        var message = format_event_message(event_type, data)
        print(message)
        
        match event_type:
            "player_died":
                player_died.emit()
            "enemy_spotted":
                enemy_spotted.emit(data.get("enemy"))
            "level_completed":
                level_completed.emit()
```

## Migration & Refactoring Guidelines

### 1. When Refactoring
```
- Maintain backward compatibility when possible
- Use feature flags for gradual rollouts
- Create migration paths for existing data
- Test thoroughly before deployment
- Document breaking changes
```

### 2. Adding New Features
```
- Design for extensibility from the start
- Use interfaces for future flexibility
- Consider impact on existing systems
- Plan for backward compatibility
- Document new patterns and conventions
```

## Quality Assurance

### 1. Code Review Checklist
- [ ] Follows SOLID principles
- [ ] No code duplication (DRY)
- [ ] Proper error handling
- [ ] Clear naming conventions
- [ ] Appropriate documentation
- [ ] Performance considerations
- [ ] Testability maintained
- [ ] Modular design

### 2. Architecture Review
- [ ] Component boundaries are clear
- [ ] Dependencies are properly managed
- [ ] Interfaces are well-defined
- [ ] System interactions are documented
- [ ] Performance implications considered
- [ ] Scalability maintained

## Godot-Specific Best Practices

### 1. Scene Design
```
- Keep scenes focused and single-purpose
- Use proper node hierarchy
- Leverage Godot's built-in systems
- Create reusable scene templates
- Use proper signal connections
```

### 2. Resource Management
```
- Use custom resources for complex data
- Implement proper resource loading
- Use ResourceLoader for dynamic content
- Create resource-based configuration
- Leverage Godot's resource system
```

### 3. Performance Optimization
```
- Use object pooling for frequent operations
- Implement proper cleanup
- Use spatial partitioning for large worlds
- Optimize update loops
- Profile performance bottlenecks
```

## LLM-Friendly Code Examples

### BAD: Complex, Interconnected Code (LLM Nightmare)
```gdscript
# DON'T DO THIS - LLMs struggle with this
func _physics_process(delta: float) -> void:
    if _state == State.DEAD: return
    if not is_on_floor(): velocity.y -= _gravity * delta
    if not is_instance_valid(_player):
        _resolve_player()
        if not is_instance_valid(_player):
            if debug_enabled and not _player_not_found_warning: 
                print("[Knight] Cannot find player. Knight is idle.")
            velocity.x = move_toward(velocity.x, 0, 0.1)
            velocity.z = move_toward(velocity.z, 0, 0.1)
            move_and_slide()
            return
    var dist := global_position.distance_to(_player.global_position)
    if dist > active_distance and not _force_chase:
        velocity.x = move_toward(velocity.x, 0, 0.1)
        velocity.z = move_toward(velocity.z, 0, 0.1)
        move_and_slide()
        return
    # ... 50 more lines of mixed logic
```

### GOOD: Functional, LLM-Friendly Code
```gdscript
# DO THIS - LLMs can focus on one function at a time
func _physics_process(delta: float) -> void:
    if is_dead(): return
    
    apply_gravity(delta)
    
    var player = get_valid_player()
    if not player:
        handle_no_player_state()
        return
    
    var distance = calculate_distance_to_player(player)
    if is_player_too_far(distance):
        handle_player_too_far_state()
        return
    
    update_ai_state(delta, player, distance)

# Pure functions - each does ONE thing
func is_dead() -> bool:
    return _state == State.DEAD

func apply_gravity(delta: float) -> void:
    if not is_on_floor():
        velocity.y -= _gravity * delta

func get_valid_player() -> Node3D:
    if not is_instance_valid(_player):
        _resolve_player()
    return _player if is_instance_valid(_player) else null

func calculate_distance_to_player(player: Node3D) -> float:
    return global_position.distance_to(player.global_position)

func is_player_too_far(distance: float) -> bool:
    return distance > active_distance and not _force_chase

func handle_no_player_state() -> void:
    if debug_enabled and not _player_not_found_warning:
        print("[Knight] Cannot find player. Knight is idle.")
    stop_movement()
    move_and_slide()

func handle_player_too_far_state() -> void:
    stop_movement()
    move_and_slide()

func stop_movement() -> void:
    velocity.x = move_toward(velocity.x, 0, 0.1)
    velocity.z = move_toward(velocity.z, 0, 0.1)
```

Remember: These rules are designed to create maintainable, flexible, and scalable code that's LLM-friendly. Always prioritize clarity and maintainability over cleverness. When in doubt, choose the more explicit and readable approach. **Turn everything into small, focused functions!** 